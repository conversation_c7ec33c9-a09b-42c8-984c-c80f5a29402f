import { use<PERSON>emo, useCallback, useState } from "react";
import type { <PERSON>atasource, IGetRowsParams, GridApi } from "ag-grid-community";
import { getProjectListApi } from "../../../redux/action/proDashAction";

// Define types locally to avoid module import issues
interface IProjectListParmas {
  start?: number;
  limit?: number;
  page: number;
  search?: string;
  filter?: any;
  order_by_name?: string;
  order_by_dir?: string;
}

interface IProjectData {
  id: number;
  project_id: string;
  project_name: string;
  customer_name?: string;
  project_status?: string;
  start_date?: string;
  end_date?: string;
  [key: string]: any;
}

interface IProjectListApiRes {
  success: boolean;
  message: string;
  data?: {
    data: IProjectData[];
  };
}

interface UseProjectInfiniteGridProps {
  search?: string;
  filter?: any;
  onDataChange?: (data: IProjectData[]) => void;
  cacheBlockSize?: number;
}

interface UseProjectInfiniteGridReturn {
  dataSource: IDatasource;
  loading: boolean;
  error: string | null;
  refreshGrid: (gridApi: GridApi) => void;
  clearError: () => void;
}

export const useProjectInfiniteGrid = ({
  search = "",
  filter,
  onDataChange,
  cacheBlockSize = 20,
}: UseProjectInfiniteGridProps): UseProjectInfiniteGridReturn => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Optimized data source with proper error handling and caching
  const dataSource = useMemo<IDatasource>(() => ({
    rowCount: undefined, // Unknown row count for infinite scrolling
    
    getRows: async (params: IGetRowsParams) => {
      const { startRow, endRow, successCallback, failCallback } = params;
      
      try {
        setLoading(true);
        setError(null);
        
        console.log(`Fetching rows ${startRow} to ${endRow}`);
        
        // Prepare API parameters
        const apiParams: IProjectListParmas = {
          start: startRow,
          limit: endRow - startRow,
          page: Math.floor(startRow / (endRow - startRow)) + 1,
          search: search || undefined,
          filter: filter || {},
          order_by_name: "",
          order_by_dir: "",
        };

        // Call the project list API
        const response = (await getProjectListApi(apiParams)) as IProjectListApiRes;

        if (response?.success && response.data?.data) {
          const projectData = response.data.data;
          
          // Notify parent component of data change
          onDataChange?.(projectData);
          
          // Determine if this is the last page
          const lastRow = projectData.length < (endRow - startRow) 
            ? startRow + projectData.length 
            : undefined;
          
          // Success callback with data and last row info
          successCallback(projectData, lastRow);
          
          console.log(`Successfully loaded ${projectData.length} rows`);
        } else {
          // Handle API error response
          const errorMessage = response?.message || "Failed to fetch project data";
          setError(errorMessage);
          failCallback();
          console.error("API Error:", errorMessage);
        }
      } catch (error) {
        // Handle network or other errors
        const errorMessage = error instanceof Error ? error.message : "Unknown error occurred";
        setError(errorMessage);
        failCallback();
        console.error("Network Error:", error);
      } finally {
        setLoading(false);
      }
    },
  }), [search, filter, onDataChange, cacheBlockSize]);

  // Refresh grid function
  const refreshGrid = useCallback((gridApi: GridApi) => {
    if (gridApi) {
      gridApi.setDatasource(dataSource);
    }
  }, [dataSource]);

  // Clear error function
  const clearError = useCallback(() => {
    setError(null);
  }, []);

  return {
    dataSource,
    loading,
    error,
    refreshGrid,
    clearError,
  };
};

export type { IProjectData, IProjectListParmas, IProjectListApiRes };
