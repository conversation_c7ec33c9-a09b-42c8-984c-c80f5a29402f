"use client";
import React, { StrictMode, useCallback, useMemo, useState } from "react";

import type { ColDef, GridReadyEvent, IDatasource } from "ag-grid-community";

import { AgGridReact } from "ag-grid-react";
interface DataDummy {
  value?: string | number;
}
const ProjectList = () => {
  // const containerStyle = useMemo(() => ({ width: "100%", height: "100%" }), []);
  // const gridStyle = useMemo(() => ({ height: "100%", width: "100%" }), []);

  const [columnDefs] = useState<ColDef[]>([
    // this row shows the row index, doesn't use any data from the row
    {
      headerName: "ID",
      maxWidth: 100,
      // it is important to have node.id here, so that when the id changes (which happens
      // when the row is loaded) then the cell is refreshed.
      valueGetter: "node.id",
      cellRenderer: (props: DataDummy) => {
        if (props.value !== undefined) {
          return props.value;
        } else {
          return (
            <img src="https://www.ag-grid.com/example-assets/loading.gif" />
          );
        }
      },
    },
    { field: "title", minWidth: 150 },
    { field: "url" },
    { field: "thumbnailUrl", minWidth: 150 },
    { field: "id" },
    // { field: "date", minWidth: 150 },
    // { field: "sport", minWidth: 150 },
    // { field: "gold" },
    // { field: "silver" },
    // { field: "bronze" },
    // { field: "total" },
  ]);
  const defaultColDef = useMemo<ColDef>(() => {
    return {
      flex: 1,
      minWidth: 100,
      sortable: false,
    };
  }, []);

  const onGridReady = useCallback((params: GridReadyEvent) => {
    fetch(`https://jsonplaceholder.typicode.com/photos?_start=${0}&_end=${20}`)
      .then((resp) => resp.json())
      .then((data) => {
        const dataSource: IDatasource = {
          rowCount: undefined,
          getRows: (rowParams) => {
            console.log(
              "asking for " + rowParams.startRow + " to " + rowParams.endRow
            );
            fetch(
              `https://jsonplaceholder.typicode.com/photos?_start=${
                rowParams.startRow ?? 0
              }&_end=${rowParams.endRow ?? 20}`
            )
              .then((resp) => resp.json())
              .then((dataB) => {
                // If we get less than requested, this was the last page:
                const lastRow =
                  dataB.length < rowParams.endRow - rowParams.startRow
                    ? rowParams.startRow + dataB.length
                    : undefined; // undefined means "keep loading"
                rowParams.successCallback(dataB, lastRow);
              });
          },
        };
        params.api.setDatasource(dataSource);
      });
  }, []);

  return (
    <div className="ag-theme-alpine" style={{ height: 600, width: "100%" }}>
      <div className="ag-grid-cell-pointer ag-theme-alpine list-view-table">
        <AgGridReact
          columnDefs={columnDefs}
          defaultColDef={defaultColDef}
          rowBuffer={0}
          da
          rowModelType="infinite"
          cacheBlockSize={20} // Number of rows per page
          maxConcurrentDatasourceRequests={1} // Only one request at a time
          maxBlocksInCache={2} // Keep only 2 blocks in memory
          onGridReady={onGridReady}
        />
      </div>
    </div>
  );
};
export default ProjectList;
// import React, {
//   useCallback,
//   useEffect,
//   useMemo,
//   useRef,
//   useState,
// } from "react";
// import { GridReadyEvent, SortChangedEvent } from "ag-grid-community";
// import { withErrorBoundary } from "react-error-boundary";
// import isEmpty from "lodash/isEmpty";
// import isEqual from "lodash/isEqual";

// // Components
// import { NoRecords } from "~/shared/components/molecules/noRecords";
// import { Typography } from "~/shared/components/atoms/typography";
// import { Tooltip } from "~/shared/components/atoms/tooltip";
// import { Progress } from "~/shared/components/atoms/progress";

// // Hooks and utilities
// import { useTranslation } from "~/hook";
// import useTableGridData from "~/shared/hooks/useTableGridData";
// import { useCurrencyFormatter } from "~/shared/components/organisms/currencyFormatterContext/CurrencyFormatterContext";
// import { useModuleAccess } from "../../hook/useModuleAcces";

// // Redux and API
// import { getProjectListApi } from "../../redux/action/proDashAction";

// // Utils and constants
// import { escapeHtmlEntities, sanitizeString } from "~/helpers/helper";
// // import { getGModuleDashboard, getGSettings } from "~/zustand"; // Will be used in later phases
// import { fnumProjectList } from "../utils/common-util";
// import { AgGridReact } from "ag-grid-react";

// // Memoized components for performance
// const MemoTypography = React.memo(Typography);
// const MemoTooltip = React.memo(Tooltip);

// interface ProjectListProps {
//   setAddProjectOpen: (value: boolean) => void;
//   search: string;
// }

// const ProjectList: React.FC<ProjectListProps> = ({
//   setAddProjectOpen,
//   search,
// }) => {
//   const { _t } = useTranslation();
//   const { datasource, gridRowParams } = useTableGridData<IProjectData>();
//   const { formatter } = useCurrencyFormatter();
//   const { module_access, hasEnoughAccessofProjectFinanceTabModule } =
//     useModuleAccess();

//   // Global state (will be used in later phases)
//   // const gModuleDashboard = getGModuleDashboard();
//   // const gSettings = getGSettings();

//   // Refs for tracking previous values and grid refresh
//   const previousValues = useRef({
//     search: "",
//   });
//   const searchTimeoutRef = useRef<NodeJS.Timeout | null>(null);

//   // Filter state (will be expanded in later phases)
//   const [filter] = useState<IProjectListFilter>({});

//   // Loading states
//   const [isSearching, setIsSearching] = useState(false);

//   // Grid refresh function
//   const refreshAgGrid = useCallback(() => {
//     const gridParams = gridRowParams?.gridParams;
//     if (gridParams) {
//       gridParams.api.setServerSideDatasource({ getRows: () => {} });
//       gridParams.api.setServerSideDatasource(datasource);
//     }
//   }, [gridRowParams, datasource]);

//   // Fetch project list data
//   const fetchProjectList = useCallback(
//     async (gridData: any) => {
//       const { changeGridParams, gridParams } = gridData;
//       const { start, length, order_by_name, order_by_dir } = changeGridParams;

//       try {
//         gridParams?.api.hideOverlay();

//         const dataParams: IProjectListParmas = {
//           start,
//           limit: length,
//           page: Math.floor(start / length),
//           search: search.trim() ? escapeHtmlEntities(search) : undefined,
//           filter: !isEmpty(filter) ? filter : undefined,
//           order_by_name,
//           order_by_dir,
//           is_kanban: false,
//         };
//         if (!dataParams.order_by_name || dataParams.order_by_name == "") {
//           delete dataParams.order_by_name; // Remove page if not needed
//         }
//         if (!dataParams.order_by_dir || dataParams.order_by_dir == "") {
//           delete dataParams.order_by_dir; // Remove page if not needed
//         }

//         const response = (await getProjectListApi(
//           dataParams
//         )) as IProjectListApiRes;

//         const rowCount = gridParams?.api?.getDisplayedRowCount() ?? 0;
//         let gridDataResult = {
//           rowCount: response?.data?.data?.length || 0,
//           rowData: response?.data?.data || [],
//         };

//         if (response?.data?.data?.length < length) {
//           gridDataResult = {
//             ...gridDataResult,
//             rowCount: rowCount + (response?.data?.data?.length ?? 0) - 1,
//           };
//         }

//         gridParams?.success(gridDataResult);

//         if (
//           (!response?.success || gridDataResult.rowData.length <= 0) &&
//           dataParams?.page === 0
//         ) {
//           gridParams?.api.showNoRowsOverlay();
//         } else if (response?.success && gridDataResult.rowData.length > 0) {
//           gridParams?.api.hideOverlay();
//         }
//       } catch (err) {
//         console.error("Error fetching project list:", err);
//         gridParams?.success({ rowCount: 0, rowData: [] });
//         gridParams?.api.showNoRowsOverlay();
//         gridParams?.fail();
//       }
//     },
//     [search, filter]
//   );

//   // Effect to trigger data fetch when grid parameters change
//   useEffect(() => {
//     if (gridRowParams?.changeGridParams) {
//       fetchProjectList(gridRowParams);
//     }
//   }, [gridRowParams, fetchProjectList]);

//   // Debounced search effect with loading states
//   useEffect(() => {
//     const currentValues = {
//       search,
//     };

//     if (!isEqual(previousValues.current, currentValues)) {
//       previousValues.current = currentValues;

//       // Clear existing timeout
//       if (searchTimeoutRef.current) {
//         clearTimeout(searchTimeoutRef.current);
//       }

//       // Set loading state immediately for search changes
//       if (search !== previousValues.current.search) {
//         setIsSearching(true);
//       }

//       // Debounce the search with 400ms delay
//       searchTimeoutRef.current = setTimeout(() => {
//         refreshAgGrid();
//         setIsSearching(false);
//       }, 400);
//     }

//     // Cleanup timeout on unmount
//     return () => {
//       if (searchTimeoutRef.current) {
//         clearTimeout(searchTimeoutRef.current);
//       }
//     };
//   }, [search, refreshAgGrid]);

//   // Grid event handlers
//   const onGridReady = useCallback(
//     (gridParams: GridReadyEvent) => {
//       gridParams?.api?.setServerSideDatasource(datasource);
//     },
//     [datasource]
//   );

//   const onSortChanged = useCallback(
//     (params: SortChangedEvent) => {
//       params.api.setServerSideDatasource({ getRows: () => {} });
//       params.api.setServerSideDatasource(datasource);
//     },
//     [datasource]
//   );

//   // Helper function for sanitizing text
//   const getSanitizedText = useCallback(
//     (text: string) => HTMLEntities.decode(sanitizeString(text)),
//     []
//   );

//   // Currency formatter helper
//   const numFormatterWithoutCurrencyValues = useCallback(
//     (value: number) => {
//       const rounded = Math.round(Number(value ?? 0) / 100);

//       return {
//         originalVal: formatter(
//           Number(value) !== 0 ? (Number(value) / 100)?.toFixed(2) : "0"
//         ).value_with_symbol,
//         roundedVal: formatter(rounded !== 0 ? rounded?.toString() : "0").value,
//       };
//     },
//     [formatter]
//   );

//   // Column definitions
//   const columnDefs = useMemo(
//     () => [
//       {
//         headerName: _t("Project"),
//         field: "project_name",
//         minWidth: 130,
//         flex: 1,
//         resizable: true,
//         suppressMovable: false,
//         suppressMenu: true,
//         sortable: true,
//         cellRenderer: (params: IProjectTableCellRenderer) => {
//           const { data } = params;
//           const title = getSanitizedText(data?.project_name ?? "");

//           return (
//             <div className="flex items-center gap-2">
//               <MemoTooltip title={_t("Project Color")} placement="top">
//                 <div className="!min-w-[10px] !max-w-[10px]">
//                   <MemoTypography
//                     className="h-2.5 w-2.5 !min-w-[10px] block rounded-full"
//                     style={{
//                       backgroundColor: data.project_color || "#000000",
//                     }}
//                   ></MemoTypography>
//                 </div>
//               </MemoTooltip>
//               <MemoTooltip title={title}>
//                 <MemoTypography className="table-tooltip-text text-center">
//                   {title}
//                 </MemoTypography>
//               </MemoTooltip>
//             </div>
//           );
//         },
//       },
//       {
//         headerName: "Project #",
//         field: "project_id",
//         flex: 1,
//         minWidth: 120,
//         resizable: true,
//         suppressMovable: false,
//         suppressMenu: true,
//         sortable: true,
//         cellRenderer: (params: IProjectTableCellRenderer) => {
//           const { data } = params;
//           const project_id = getSanitizedText(data.project_id ?? "");

//           return data.project_id ? (
//             <MemoTooltip title={project_id}>
//               <MemoTypography className="table-tooltip-text">
//                 {project_id}
//               </MemoTypography>
//             </MemoTooltip>
//           ) : (
//             "-"
//           );
//         },
//       },
//       {
//         headerName: _t("Complete (%)"),
//         field: "progress",
//         maxWidth: 135,
//         minWidth: 135,
//         headerClass: "ag-header-center",
//         suppressMovable: false,
//         suppressMenu: true,
//         sortable: true,
//         cellRenderer: (params: IProjectTableCellRenderer) => {
//           const { data } = params;

//           return (
//             <div className="text-center project-percent">
//               <Progress
//                 size={27}
//                 type="circle"
//                 trailColor={"#00800080"}
//                 strokeWidth={9}
//                 strokeColor={"#008000"}
//                 percent={Math.floor(Number(data.progress) * 100)}
//                 format={() =>
//                   `${fnumProjectList(Math.floor(Number(data.progress) * 100))}%`
//                 }
//               />
//             </div>
//           );
//         },
//       },
//       {
//         headerName: _t("Project Amt."),
//         field: "no_tax_contract_amount",
//         maxWidth: 130,
//         minWidth: 130,
//         headerClass: "ag-header-right",
//         cellClass: "ag-right-aligned-cell",
//         hide: !hasEnoughAccessofProjectFinanceTabModule,
//         suppressMovable: false,
//         suppressMenu: true,
//         sortable: true,
//         cellRenderer: (params: IProjectTableCellRenderer) => {
//           const { data } = params;
//           const { originalVal: tooltipVal, roundedVal } =
//             numFormatterWithoutCurrencyValues(
//               Number(data.no_tax_contract_amount ?? 0)
//             );

//           return (
//             <MemoTooltip title={`${tooltipVal}`}>
//               <MemoTypography className="table-tooltip-text">
//                 {roundedVal}
//               </MemoTypography>
//             </MemoTooltip>
//           );
//         },
//       },
//     ],
//     [
//       _t,
//       getSanitizedText,
//       numFormatterWithoutCurrencyValues,
//       hasEnoughAccessofProjectFinanceTabModule,
//     ]
//   );

//   return (
//     <div
//       className={`ag-grid-cell-pointer ag-theme-alpine list-view-table ${
//         module_access === "read_only"
//           ? "md:h-[calc(100dvh-308px)] h-[calc(100dvh-326px)]"
//           : "md:h-[calc(100dvh-270px)] h-[calc(100dvh-304px)]"
//       }`}
//     >
//       <AgGridReact
//         columnDefs={columnDefs}
//         onGridReady={onGridReady}
//         onSortChanged={onSortChanged}
//         suppressAggFuncInHeader
//         // rowModelType="serverSide"
//         rowHeight={34}
//         headerHeight={32}
//         rowBuffer={20}
//         rowModelType="infinite"
//         cacheBlockSize={20} // page size
//         maxBlocksInCache={5} // adjust for memory
//         pagination={false}
//         // cacheBlockSize={20}
//         // enableOpenInNewTab={window.ENV.ENABLE_ALL_CLICK}
//         // generateOpenInNewTabUrl={(data: { id?: number }) =>
//         //   `${routes.MANAGE_PROJECT.url}/${data?.id}${
//         //     !hasEnoughAccessofProjectFinanceTabModule ? "/details" : ""
//         //   }`
//         // }
//         // restrictOpenInNewTabFields={["email"]}

//         noRowsOverlayComponent={() => (
//           <NoRecords
//             rootClassName="w-full max-w-[280px]"
//             image={`${window.ENV.CDN_URL}assets/images/create-record-list-view.svg`}
//             text={
//               module_access === "full_access" ||
//               module_access === "own_data_access" ? (
//                 <div>
//                   <MemoTypography
//                     onClick={() => setAddProjectOpen(true)}
//                     className="sm:text-base text-xs underline underline-offset-1 text-black font-bold cursor-pointer"
//                   >
//                     {_t("Click here")}
//                   </MemoTypography>
//                   <MemoTypography className="sm:text-base text-xs text-black font-semibold">
//                     {_t(" to Create a New Record")}
//                   </MemoTypography>
//                 </div>
//               ) : (
//                 <MemoTypography className="sm:text-base text-xs text-black font-semibold">
//                   {_t("No Record Found")}
//                 </MemoTypography>
//               )
//             }
//           />
//         )}
//       />
//     </div>
//   );
// };

// export default withErrorBoundary(React.memo(ProjectList), {
//   FallbackComponent: ({ error }) => (
//     <NoRecords
//       className="h-full"
//       image={`https://cdn.contractorforeman.net/assets/images/unknown-error.svg`}
//       text={error.message || "Something went wrong!"}
//     />
//   ),
// });
