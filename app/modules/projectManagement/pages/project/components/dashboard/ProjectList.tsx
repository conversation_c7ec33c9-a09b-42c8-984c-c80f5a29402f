"use client";
import React, {
  use<PERSON><PERSON>back,
  useMemo,
  useState,
  useRef,
  useEffect,
} from "react";
import type { ColDef, GridReadyEvent, GridApi } from "ag-grid-community";
import { AgGridReact } from "ag-grid-react";
import {
  useProjectInfiniteGrid,
  type IProjectData,
} from "./hooks/useProjectInfiniteGrid";

// Loading cell renderer component
const LoadingCellRenderer = () => (
  <div className="flex items-center justify-center">
    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500"></div>
  </div>
);

// Error cell renderer component
const ErrorCellRenderer = () => (
  <div className="text-red-500 text-sm">Failed to load</div>
);

interface ProjectListProps {
  filter?: any;
  search?: string;
  onDataChange?: (data: IProjectData[]) => void;
}
const ProjectList: React.FC<ProjectListProps> = ({
  filter,
  search = "",
  onDataChange,
}) => {
  const gridRef = useRef<AgGridReact>(null);
  const [gridApi, setGridApi] = useState<GridApi | null>(null);

  // Use the custom hook for infinite grid functionality
  const { dataSource, loading, error, refreshGrid, clearError } =
    useProjectInfiniteGrid({
      search,
      filter,
      onDataChange,
      cacheBlockSize: 20,
    });

  // Column definitions optimized for project data
  const columnDefs = useMemo<ColDef[]>(
    () => [
      {
        headerName: "Row",
        maxWidth: 80,
        valueGetter: "node.rowIndex + 1",
        cellRenderer: (params: any) => {
          if (params.value !== undefined) {
            return params.value;
          }
          return <LoadingCellRenderer />;
        },
        pinned: "left",
        sortable: false,
        filter: false,
      },
      {
        field: "project_name",
        headerName: "Project Name",
        minWidth: 200,
        flex: 2,
        cellRenderer: (params: any) => {
          if (!params.data) return <LoadingCellRenderer />;
          if (params.data.error) return <ErrorCellRenderer />;
          return params.value || "N/A";
        },
      },
      {
        field: "project_id",
        headerName: "Project ID",
        minWidth: 120,
        flex: 1,
      },
      {
        field: "customer_name",
        headerName: "Customer",
        minWidth: 150,
        flex: 1,
      },
      {
        field: "project_status",
        headerName: "Status",
        minWidth: 120,
        flex: 1,
      },
      {
        field: "start_date",
        headerName: "Start Date",
        minWidth: 120,
        flex: 1,
        valueFormatter: (params) => {
          if (!params.value) return "N/A";
          return new Date(params.value).toLocaleDateString();
        },
      },
      {
        field: "end_date",
        headerName: "End Date",
        minWidth: 120,
        flex: 1,
        valueFormatter: (params) => {
          if (!params.value) return "N/A";
          return new Date(params.value).toLocaleDateString();
        },
      },
    ],
    []
  );

  const defaultColDef = useMemo<ColDef>(
    () => ({
      flex: 1,
      minWidth: 100,
      sortable: true,
      filter: true,
      resizable: true,
      suppressMenu: false,
    }),
    []
  );

  // Grid ready handler
  const onGridReady = useCallback(
    (params: GridReadyEvent) => {
      setGridApi(params.api);
      params.api.setDatasource(dataSource);
    },
    [dataSource]
  );

  // Refresh grid when search or filter changes
  useEffect(() => {
    if (gridApi) {
      refreshGrid(gridApi);
    }
  }, [gridApi, refreshGrid]);

  return (
    <div className="w-full h-full">
      {/* Error display */}
      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg
                className="h-5 w-5 text-red-400"
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path
                  fillRule="evenodd"
                  d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                  clipRule="evenodd"
                />
              </svg>
            </div>
            <div className="ml-3 flex-1">
              <h3 className="text-sm font-medium">Error loading projects</h3>
              <div className="mt-2 text-sm">
                <p>{error}</p>
              </div>
            </div>
            <div className="ml-auto pl-3">
              <button
                onClick={clearError}
                className="inline-flex text-red-400 hover:text-red-600 focus:outline-none focus:text-red-600"
                aria-label="Dismiss error"
              >
                <svg
                  className="h-5 w-5"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                >
                  <path
                    fillRule="evenodd"
                    d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                    clipRule="evenodd"
                  />
                </svg>
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Loading indicator */}
      {loading && (
        <div className="flex items-center justify-center py-4">
          <LoadingCellRenderer />
          <span className="ml-2 text-gray-600">Loading projects...</span>
        </div>
      )}

      {/* AgGrid Table */}
      <div className="ag-theme-alpine" style={{ height: 600, width: "100%" }}>
        <div className="ag-grid-cell-pointer ag-theme-alpine list-view-table">
          <AgGridReact
            ref={gridRef}
            columnDefs={columnDefs}
            defaultColDef={defaultColDef}
            rowModelType="infinite"
            cacheBlockSize={20} // Optimized page size
            maxConcurrentDatasourceRequests={2} // Allow 2 concurrent requests for better performance
            maxBlocksInCache={10} // Increased cache for better UX
            rowBuffer={5} // Buffer rows for smoother scrolling
            cacheOverflowSize={2} // Additional cache overflow
            infiniteInitialRowCount={20} // Initial row count estimate
            onGridReady={onGridReady}
            suppressRowClickSelection={true}
            rowSelection="single"
            animateRows={true}
            enableCellTextSelection={true}
            suppressCellFocus={true}
            // Performance optimizations
            suppressColumnVirtualisation={false}
            suppressRowVirtualisation={false}
            // Loading overlay
            loadingOverlayComponent={() => (
              <div className="flex items-center justify-center h-full">
                <LoadingCellRenderer />
                <span className="ml-2">Loading projects...</span>
              </div>
            )}
            // No rows overlay
            noRowsOverlayComponent={() => (
              <div className="flex flex-col items-center justify-center h-full text-gray-500">
                <svg
                  className="w-12 h-12 mb-4"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                  />
                </svg>
                <p className="text-lg font-medium">No projects found</p>
                <p className="text-sm">
                  Try adjusting your search or filter criteria
                </p>
              </div>
            )}
          />
        </div>
      </div>
    </div>
  );
};

export default ProjectList;
