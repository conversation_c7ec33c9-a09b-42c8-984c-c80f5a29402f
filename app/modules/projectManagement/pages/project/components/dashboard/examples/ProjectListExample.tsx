import React, { useState, useCallback } from "react";
import Project<PERSON>ist from "../ProjectList";
import type { IProjectData } from "../hooks/useProjectInfiniteGrid";

/**
 * Example implementation of ProjectList component
 * This shows how to integrate the optimized infinite scroll project list
 * with search, filtering, and data handling capabilities.
 */
const ProjectListExample: React.FC = () => {
  // State for search and filter
  const [search, setSearch] = useState("");
  const [filter, setFilter] = useState({
    project_status: "",
    customer: "",
    project_type: "",
  });

  // State for loaded data (optional - for additional processing)
  const [loadedProjects, setLoadedProjects] = useState<IProjectData[]>([]);
  const [totalLoadedCount, setTotalLoadedCount] = useState(0);

  // Handle data changes from the grid
  const handleDataChange = useCallback((data: IProjectData[]) => {
    // This callback is called every time new data is loaded
    // You can use this to:
    // 1. Track loaded data
    // 2. Update other components
    // 3. Perform analytics
    // 4. Cache data locally
    
    console.log(`Loaded ${data.length} projects:`, data);
    
    // Example: Keep track of all loaded projects
    setLoadedProjects(prev => {
      const existingIds = new Set(prev.map(p => p.id));
      const newProjects = data.filter(p => !existingIds.has(p.id));
      return [...prev, ...newProjects];
    });
    
    // Update total count
    setTotalLoadedCount(prev => prev + data.length);
  }, []);

  // Handle search input
  const handleSearchChange = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    setSearch(event.target.value);
    // Reset loaded data when search changes
    setLoadedProjects([]);
    setTotalLoadedCount(0);
  }, []);

  // Handle filter changes
  const handleFilterChange = useCallback((key: string, value: string) => {
    setFilter(prev => ({
      ...prev,
      [key]: value,
    }));
    // Reset loaded data when filter changes
    setLoadedProjects([]);
    setTotalLoadedCount(0);
  }, []);

  // Clear all filters
  const handleClearFilters = useCallback(() => {
    setSearch("");
    setFilter({
      project_status: "",
      customer: "",
      project_type: "",
    });
    setLoadedProjects([]);
    setTotalLoadedCount(0);
  }, []);

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold text-gray-900">Projects</h1>
        <div className="text-sm text-gray-500">
          {totalLoadedCount > 0 && `${totalLoadedCount} projects loaded`}
        </div>
      </div>

      {/* Search and Filter Controls */}
      <div className="bg-white p-4 rounded-lg shadow-sm border space-y-4">
        {/* Search */}
        <div>
          <label htmlFor="search" className="block text-sm font-medium text-gray-700 mb-2">
            Search Projects
          </label>
          <input
            id="search"
            type="text"
            value={search}
            onChange={handleSearchChange}
            placeholder="Search by project name, ID, or customer..."
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>

        {/* Filters */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label htmlFor="status-filter" className="block text-sm font-medium text-gray-700 mb-2">
              Project Status
            </label>
            <select
              id="status-filter"
              value={filter.project_status}
              onChange={(e) => handleFilterChange("project_status", e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="">All Statuses</option>
              <option value="active">Active</option>
              <option value="completed">Completed</option>
              <option value="on_hold">On Hold</option>
              <option value="cancelled">Cancelled</option>
            </select>
          </div>

          <div>
            <label htmlFor="customer-filter" className="block text-sm font-medium text-gray-700 mb-2">
              Customer
            </label>
            <input
              id="customer-filter"
              type="text"
              value={filter.customer}
              onChange={(e) => handleFilterChange("customer", e.target.value)}
              placeholder="Filter by customer..."
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>

          <div>
            <label htmlFor="type-filter" className="block text-sm font-medium text-gray-700 mb-2">
              Project Type
            </label>
            <select
              id="type-filter"
              value={filter.project_type}
              onChange={(e) => handleFilterChange("project_type", e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="">All Types</option>
              <option value="residential">Residential</option>
              <option value="commercial">Commercial</option>
              <option value="industrial">Industrial</option>
            </select>
          </div>
        </div>

        {/* Clear Filters Button */}
        <div className="flex justify-end">
          <button
            onClick={handleClearFilters}
            className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2"
          >
            Clear All Filters
          </button>
        </div>
      </div>

      {/* Project List */}
      <div className="bg-white rounded-lg shadow-sm border">
        <ProjectList
          search={search}
          filter={filter}
          onDataChange={handleDataChange}
        />
      </div>

      {/* Debug Info (remove in production) */}
      {process.env.NODE_ENV === 'development' && (
        <div className="bg-gray-50 p-4 rounded-lg">
          <h3 className="text-sm font-medium text-gray-700 mb-2">Debug Info</h3>
          <div className="text-xs text-gray-600 space-y-1">
            <div>Search: "{search}"</div>
            <div>Filter: {JSON.stringify(filter, null, 2)}</div>
            <div>Unique Projects Loaded: {loadedProjects.length}</div>
            <div>Total Load Events: {totalLoadedCount}</div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ProjectListExample;

/**
 * Usage in a route or parent component:
 * 
 * import ProjectListExample from './components/dashboard/examples/ProjectListExample';
 * 
 * function ProjectsPage() {
 *   return <ProjectListExample />;
 * }
 */
