import React, {
  lazy,
  startTransition,
  Suspense,
  useEffect,
  useRef,
  useState,
} from "react";
import { useCallback, useMemo } from "react";
import {
  getGModuleDashboard,
  getGModuleFilters,
  setGModuleFilter,
  setIsFilterBeingApplied,
  setIsFilterUpdated,
  useGModules,
} from "~/zustand";
import { useTranslation } from "~/hook";
import { RadioChangeEvent, Spin } from "antd";
// Atoms
import { Button } from "~/shared/components/atoms/button";
import { Popover } from "~/shared/components/atoms/popover";
import { Typography } from "~/shared/components/atoms/typography";
import { FontAwesomeIcon } from "~/shared/components/atoms/fontAwesomeIcon";
// molecules
import { ButtonWithTooltip } from "~/shared/components/molecules/buttonWithTooltip";
import { AccordionButton } from "~/shared/components/molecules/accordionButton";
import { AddButton } from "~/shared/components/molecules/addButton";
import { ErrorBoundary } from "~/shared/components/molecules/errorBoundary/ErrorBoundary";
import { CustomCheckBox } from "~/shared/components/molecules/customCheckBox";
import { DashboardHeader } from "~/shared/components/molecules/dashboardHeader";
import { ListTabButton } from "~/shared/components/molecules/listTabButton";
import { ReadOnlyPermissionMsg } from "~/shared/components/molecules/readOnlyPermissionMsg";
// Organisms
import { KanbanList } from "~/shared/components/organisms/kanbanView";
// Other
import { GRID_BUTTON_TAB } from "~/modules/projectManagement/pages/project/utils/constants";
import ProjectFilter from "~/modules/projectManagement/pages/project/components/dashboard/ProjectFilter";
import {
  useAppProDispatch,
  useAppProSelector,
} from "~/modules/projectManagement/pages/project/redux/store";
import {
  fetchProDashboardApi,
  getProjectKanbanListApi,
  updateProjectApi,
} from "~/modules/projectManagement/pages/project/redux/action/proDashAction";
import ProjectStoreProvider from "~/modules/projectManagement/pages/project/redux/projectStoreProvider";
import {
  setIsInitialLoad,
  setProSearchValue,
} from "~/modules/projectManagement/pages/project/redux/slices/proDashWidgetsSlice";
import { fetchProjectType } from "~/modules/projectManagement/pages/project/redux/action/addProjectAction";
import { STATUS_CODE } from "~/shared/constants";
import { escapeHtmlEntities } from "~/helpers/helper";
import isEmpty from "lodash/isEmpty";
import debounce from "lodash/debounce";
import isEqual from "lodash/isEqual";
import pullAt from "lodash/pullAt";
import uniq from "lodash/uniq";
import {
  updateKanbanSettingApi,
  updateKanbanSortingApi,
} from "~/redux/action/kanbanSettings";
// import Sortable from "sortablejs";
import { CheckboxChangeEvent } from "antd/es/checkbox";
import { isKanbanViewApi } from "~/redux/action/iskanbanView";
import { ProjectListTableDropdownItems } from "~/modules/projectManagement/pages/project/components/dashboard/ProjectListTableDropdownItems";
import {
  json,
  useLoaderData,
  useNavigate,
  useSearchParams,
} from "@remix-run/react";
import { routes } from "~/route-services/routes";

import { getGlobalUser } from "~/zustand/global/user/slice";

// FontAwesome File
import { ProjectDashboardRegularIconAdd } from "~/shared/utils/helper/fontAwesome/libraryAdd/module/project/dashboard/regular";
import { getCurrentMenuModule } from "~/zustand/global/config/slice";
import ProjectEventsMapView from "~/modules/projectManagement/pages/project/components/dashboard/ProjectEventsMapView.client";
import BarChartSkeleton from "~/shared/components/molecules/charts/skeleton/BarChart.skeleton";
import { SchedulerSkeleton } from "~/shared/components/molecules/schedulerSkeleton";
import LegendChart from "~/shared/components/molecules/charts/skeleton/LegendChart.skeleton";
import PieChartSkeleton from "~/shared/components/molecules/charts/skeleton/PieChart.skeleton";
import { useModuleAccess } from "~/modules/projectManagement/pages/project/hook/useModuleAcces";
import { TypographyLink } from "~/shared/components/atoms/typographyLink";
import dayjs from "dayjs";

// Fort Awesome Library Add icons
ProjectDashboardRegularIconAdd();

const AddProjectWrapper = lazy(
  () =>
    import(
      "~/modules/projectManagement/pages/project/components/sidebar/AddProjectWrapper"
    )
);
const ActionItems = lazy(
  () =>
    import(
      "~/modules/projectManagement/pages/project/components/dashboard/ActionItems"
    )
);
const ThisWeekTasks = lazy(
  () =>
    import(
      "~/modules/projectManagement/pages/project/components/dashboard/ThisWeekTasks.client"
    )
);
const Labor = lazy(
  () =>
    import(
      "~/modules/projectManagement/pages/project/components/dashboard/Labor"
    )
);
const UnpaidItems = lazy(
  () =>
    import(
      "~/modules/projectManagement/pages/project/components/dashboard/UnpaidItems"
    )
);
const ProjectsbyStatus = lazy(
  () =>
    import(
      "~/modules/projectManagement/pages/project/components/dashboard/ProjectsbyStatus"
    )
);
const ProjectList = lazy(
  () =>
    import(
      "~/modules/projectManagement/pages/project/components/dashboard/ProjectList"
    )
);

export const loader = () => {
  return json({
    PARALLEL_SITE: process.env.PARALLEL_SITE,
    PANEL_URL: process.env.PANEL_URL,
  });
};

const ManageProjectsCom = () => {
  const { PARALLEL_SITE, PANEL_URL } = useLoaderData<typeof loader>();

  const { _t } = useTranslation();
  const navigate = useNavigate();

  const [fullScreenTable, setFullScreenTable] = useState(false);
  const [addProjectOpen, setAddProjectOpen] = useState<boolean>(false);
  const [loadingChild, setLoadingChild] = useState<boolean>(false);
  const [searchParams, setSearchParams] = useSearchParams();
  const [open, setOpen] = useState<boolean>(false);
  const [mapView, setMapView] = useState<boolean>(false);
  const [schedulerReloadKey, setSchedulerReloadKey] = useState<number>(
    Date.now()
  );
  const [viewSearch, setViewSearch] = useState<boolean>(false);
  const user: IInitialGlobalData["user"] = getGlobalUser();
  const [isLoading, setIsLoading] = useState<boolean>(false);

  const currentModule: IInitialGlobalData["config"]["current_module"] =
    getCurrentMenuModule();
  const {
    module_key = "",
    name = "Projects",
    module_id = 0,
    singular_name = "Project",
    module_access,
  } = currentModule || {};

  const limit = 15;
  const gModuleDashboard: Partial<GModuleDashboard> = getGModuleDashboard();
  const { checkModuleAccessByKey } = useGModules();
  const { hasEnoughAccessofProjectFinanceTabModule } = useModuleAccess();
  const [pageBySection, setPageBySection] = useState<{ [key: string]: number }>(
    {}
  );
  const filterSrv: Partial<ProjectFilter> | undefined = getGModuleFilters() as
    | Partial<ProjectFilter>
    | undefined;
  const [loadingBySection, setLoadingBySection] = useState<{
    [key: string]: boolean;
  }>({});
  const [kanbanListData, setKanbanListData] = useState<
    IProjectKanbanViewListData[]
  >([]);
  const [hasMoreBySection, setHasMoreBySection] = useState<
    Record<string, boolean>
  >({});

  const [isKanbanEnabled, setIsKanbanEnabled] = useState<boolean>(false);
  const [isKanbanLoading, setIsKanbanLoading] = useState<boolean>(false);

  const [kanbanSelected, setKanbanSelected] = useState<string[]>([]);
  const [projectToBeUpdate, setProjectToBeUpdate] =
    useState<IPorjectKanbanData | null>(null);
  const [kanbanSetting, setKanbanSetting] = useState<
    IKanbanSetting | IProjectKanbanSettings
  >();

  const kanbanView = useMemo(() => isKanbanEnabled, [isKanbanEnabled]);

  const [isDefaultViewKanbanLoading, setIsDefaultViewKanbanLoading] =
    useState<boolean>(false);

  const { proSearchValue } = useAppProSelector((state) => state.proDashboard);

  const isReadOnly = useMemo(
    () => checkModuleAccessByKey(module_key) === "read_only",
    [module_key]
  );

  const filter = useMemo(() => {
    return {
      start_date: filterSrv?.start_date || "",
      end_date: filterSrv?.end_date || "",
      status: filterSrv?.status || "",
      customer: filterSrv?.customer || "",
      customer_names: filterSrv?.customer_names || "",
      project_contacts: filterSrv?.project_contacts || "",
      project_contacts_names: filterSrv?.project_contacts_names || "",
      project_manager: filterSrv?.project_manager || "",
      project_manager_names: filterSrv?.project_manager_names || "",
      project_status_kanban: filterSrv?.project_status_kanban || "",
      project_status_kanban_names: filterSrv?.project_status_kanban_names || "",
      project_type: filterSrv?.project_type || "",
      project_type_names: filterSrv?.project_type_names || "",
      record_type: filterSrv?.record_type || "project",
      sales_rep: filterSrv?.sales_rep || "",
      sales_rep_names: filterSrv?.sales_rep_names || "",
      tab: filterSrv?.tab || "all",
    } as Partial<IProjectListFilter>;
  }, [filterSrv]);

  const beforeFilterUpdateCallback = () => {
    setIsFilterUpdated(false);
    setIsFilterBeingApplied(true);
  };

  const filterCallComplete = () => {
    setIsFilterUpdated(true);
  };

  useEffect(() => {
    if (searchParams?.get("action")?.trim() === "new") {
      setAddProjectOpen(true);
      if (isReadOnly) {
        setSearchParams({});
      }
    }
  }, [searchParams.get("action")]);

  const updateFilter = useCallback(
    (filter: Partial<ProjectFilter>) => {
      if (module_id) {
        beforeFilterUpdateCallback();
        setGModuleFilter(filter, user, module_id, filterCallComplete);
      }
    },
    [module_id]
  );

  const dispatch = useAppProDispatch();
  const { dashboardData, isInitialLoad, isLoadingProjectSchedule } =
    useAppProSelector((state) => state.proDashboard);

  const previousValues = useRef({
    filter: JSON.stringify(filter),
    search: proSearchValue,
  });

  useEffect(() => {
    const fetchKanbanView = async () => {
      setIsKanbanLoading(true); // Set loading state
      try {
        const response = (await isKanbanViewApi({
          key: CFConfig.project_module,
        })) as IIsKanbanEnableApiRes;

        if (response?.success) {
          setIsKanbanEnabled(response?.data?.is_kanban === 1); // Set the result based on API response
        } else {
          setIsKanbanEnabled(false); // Set false if API response indicates failure
        }
      } catch (error) {
        setIsKanbanEnabled(false); // Set false in case of an error
      } finally {
        setIsKanbanLoading(false);
      }
    };

    fetchKanbanView();
  }, []);

  useEffect(() => {
    dispatch(
      fetchProjectType({
        types: [188, 226],
        module_id: [module_id],
      })
    );
  }, [module_id]);

  useEffect(() => {
    if (isInitialLoad) {
      dispatch(
        fetchProDashboardApi({
          refresh_type: "",
          is_refresh: 0,
        })
      );
      dispatch(setIsInitialLoad(false));
    }
  }, []);

  const fetchKanbanProjectList = async (
    type: string,
    isLoad: boolean = true
  ) => {
    type === "" ? setIsLoading(true) : setLoadingChild(isLoad);

    const filter = previousValues.current?.filter
      ? JSON.parse(previousValues.current?.filter)
      : {};

    const search = previousValues.current.search;

    const pageForType = pageBySection[type] || 0;

    const tempFil: IProjectTempFil = {
      status: STATUS_CODE.ACTIVE,
      tab: "all",
    };
    if (filter?.tab) {
      tempFil.tab = filter.tab.toString() || "";
    }
    if (filter?.status !== undefined) {
      tempFil.status = filter.status;
    }
    if (filter?.project_contacts) {
      tempFil.project_contacts = filter.project_contacts;
    }
    if (filter?.sales_rep) {
      tempFil.sales_rep = filter.sales_rep;
    }
    if (filter?.project_manager) {
      tempFil.project_manager = filter.project_manager;
    }
    if (filter?.start_date) {
      tempFil.start_date = dayjs(filter.start_date).format("YYYY-MM-DD") || "";
    }
    if (filter?.project_type) {
      tempFil.project_type = filter.project_type;
    }
    if (filter?.end_date) {
      tempFil.end_date = dayjs(filter.end_date).format("YYYY-MM-DD") || "";
    }
    if (filter?.customer) {
      tempFil.customer = filter.customer;
    }
    if (filter?.project_status_kanban) {
      tempFil.project_status_kanban = filter.project_status_kanban;
    }
    if (filter?.status === STATUS_CODE.ALL) {
      delete tempFil.status;
    }
    const status = gModuleDashboard.module_setting?.module_status?.find(
      (item) => item.item_id === type
    );
    let dataParams: IProjectKanbanListParmas = {
      filter: tempFil,
      page: pageForType,
      limit: limit,
      ignore_filter: 1,
      any_status: !!status?.key ? status.key : undefined,
      search: escapeHtmlEntities(search),
    };
    if (search === "") {
      delete dataParams.search;
    }

    if (isEmpty(tempFil)) {
      delete dataParams.filter;
    }

    try {
      const resData = (await getProjectKanbanListApi(
        dataParams
      )) as IProjectKanbanListApiRes;
      if (resData?.success) {
        let newTypes = resData?.data?.kanban_data;
        newTypes = newTypes?.map((item) => {
          item.status_data.projects = item.status_data.projects?.map(
            (kanbanData) => {
              kanbanData.project_name = `${kanbanData.project_name} (${kanbanData.project_id})`;
              return kanbanData;
            }
          );
          return item;
        });

        if (pageForType === 0) {
          const data = newTypes.filter((item) => item !== null);
          const kanbanViewData = data.map((item) => item.status_data);
          setKanbanListData(kanbanViewData);
        } else {
          setKanbanListData((prevData) => {
            if (prevData.length === 0) {
              const data = newTypes.filter((item) => item !== null);
              const kanbanViewData = data.map((item) => item.status_data);
              return kanbanViewData;
            }

            const updateData = prevData.map((prevSection) => {
              const newSection = newTypes.find((d) => {
                if (d !== null) {
                  return d.status_data.item_id === prevSection.item_id;
                }
              });
              if (newSection) {
                const updatedSection = { ...prevSection };

                const newKanbanData = newSection.status_data.projects.filter(
                  (newItem) =>
                    !updatedSection.projects.some(
                      (existingItem) =>
                        existingItem.project_id === newItem.project_id
                    )
                );

                updatedSection.projects.push(...newKanbanData);
                return updatedSection;
              }
              return prevSection;
            });
            return updateData;
          });
        }
        const newHasMoreBySection = newTypes.reduce(
          (acc: Record<number, boolean>, section: IProjectKanbanListData) => {
            const sectionDataLength = section.status_data.projects.length;
            acc[section.status_data.item_id] = sectionDataLength >= limit;
            return acc;
          },
          {}
        );
        setHasMoreBySection((prev) => ({
          ...prev,
          ...newHasMoreBySection,
        }));
        setKanbanSelected(resData?.data.kanban_project_selected);
        setKanbanSetting(resData?.data.kanban_setting);
        setIsLoading(false);
        setLoadingChild(false);
      } else {
        setKanbanListData([]);
        setKanbanSetting(undefined);
        notification.error({
          description: resData?.message || "Something went wrong!",
        });
      }
      if (type !== "") {
        setLoadingBySection((prev) => ({
          ...prev,
          [type]: false,
        }));
      }
    } catch (err) {
      setIsLoading(false);
      setLoadingChild(false);
      notification.error({
        description: (err as Error).message || "Something went wrong!",
      });
      if (type !== "") {
        setLoadingBySection((prev) => ({
          ...prev,
          [type]: false,
        }));
      }
    } finally {
      setIsLoading(false);
      setIsFilterBeingApplied(false);
      setLoadingChild(false);
    }
  };

  const handleLoadMore = (val: number) => {
    const isSectionLoading = loadingBySection[val];
    if (isSectionLoading) return;

    const hasMoreForSection = hasMoreBySection[val];
    const currentSectionPage = pageBySection[val] || 0;

    if (hasMoreForSection) {
      const nextPage = currentSectionPage + 1;
      setPageBySection((prev) => ({
        ...prev,
        [val]: nextPage,
      }));

      if (currentSectionPage !== 0) {
        setLoadingBySection((prev) => ({
          ...prev,
          [val]: true,
        }));
        fetchKanbanProjectList(val.toString());
      }
    }
  };

  const handleCollapse = async (columnId: string, isCollapseCard: string) => {
    let arrayKanbanSelected = uniq(kanbanSelected);
    if (arrayKanbanSelected.includes(columnId)) {
      arrayKanbanSelected = arrayKanbanSelected.filter(
        (value) => value !== columnId
      );
    } else {
      arrayKanbanSelected.push(columnId);
    }
    try {
      const requestKanbanSetting = {
        module_field_id: arrayKanbanSelected,
        default_view: kanbanSetting?.default_view?.toString() ?? "0",
        module_id: module_id,
      };
      const responseKanbanSetting = (await updateKanbanSettingApi(
        requestKanbanSetting
      )) as IKanbanSettingApiRes;
      if (responseKanbanSetting.success) {
        setKanbanSetting(responseKanbanSetting?.data);
        setKanbanSelected(responseKanbanSetting?.kanban_project_selected);
      } else {
        notification.error({
          description: responseKanbanSetting.message || "Something went wrong!",
        });
      }
    } catch (error) {
      notification.error({
        description: (error as Error)?.message || "Something went wrong!",
      });
    } finally {
      // setIsDefaultViewKanbanLoading(false);
    }
  };

  // const handleDragDropEnd = async (event: Sortable.SortableEvent) => {
  //   const currentArray = kanbanListData?.map((data) => ({
  //     column_id: data.key,
  //     sort_order: Number(data.sort_order),
  //     sorting_id: data.sorting_id.toString(),
  //     column_name: data.name,
  //     type_id: data.item_id.toString(),
  //   }));

  //   const kanban_sorting: IKanbanSortingArray[] = currentArray.map(
  //     (data, index) => ({
  //       ...data,
  //       sort_order: index,
  //     })
  //   );

  //   try {
  //     const requestKanbanSetting: IProjectKanbanSorting = {
  //       kanban_sorting: kanban_sorting,
  //       module_id: module_id,
  //     };
  //     const responseKanbanSetting = (await updateKanbanSortingApi(
  //       requestKanbanSetting
  //     )) as IKanbanSortingApiRes;
  //     if (!responseKanbanSetting.success) {
  //       notification.error({
  //         description: responseKanbanSetting.message || "Something went wrong!",
  //       });
  //     }
  //   } catch (error) {
  //     notification.error({
  //       description: (error as Error).message || "Something went wrong!",
  //     });
  //   }
  // };

  // const handleCardDragDropEnd = async (event: Sortable.SortableEvent) => {
  //   const { from, to } = event;
  //   const fromColumnId =
  //     from?.closest("[data-id]")?.getAttribute("data-id") ?? "";
  //   const toColumnId = to?.closest("[data-id]")?.getAttribute("data-id") ?? "";

  //   let moduleStatus: ModuleStatus | undefined =
  //     gModuleDashboard?.module_setting?.module_status?.find(
  //       (moduleStatus: ModuleStatus) => {
  //         return moduleStatus?.item_id === toColumnId;
  //       }
  //     );
  //   if (fromColumnId !== toColumnId) {
  //     const data: IProjectUpdateApiParams = {
  //       project_status: moduleStatus?.key,
  //     };
  //     try {
  //       const updateRes = (await updateProjectApi({
  //         id: projectToBeUpdate?.id,
  //         ...data,
  //       })) as IProjectDetailUpdateApiRes;
  //       if (updateRes.success) {
  //         setKanbanListData((prevData) =>
  //           prevData.map((item) => {
  //             if (item.item_id == Number(toColumnId)) {
  //               return {
  //                 ...item,
  //                 total_count: (parseInt(item.total_count) + 1).toString(),
  //               };
  //             } else if (item.item_id == Number(fromColumnId)) {
  //               return {
  //                 ...item,
  //                 total_count: (parseInt(item.total_count) - 1).toString(),
  //               };
  //             }
  //             return item;
  //           })
  //         );
  //       } else {
  //         fetchKanbanProjectList("", false);
  //         notification.error({
  //           description: updateRes?.message || "Something went wrong!",
  //         });
  //       }
  //     } catch (error) {
  //       notification.error({
  //         description: (error as Error)?.message,
  //       });
  //     }
  //   }
  // };

  const handleDefaultViewKanabanChange = async (e: CheckboxChangeEvent) => {
    setIsDefaultViewKanbanLoading(true);
    try {
      const requestKanbanSetting = {
        default_view: e.target.checked ? 1 : 0,
        module_id: module_id,
      };
      const responseKanbanSetting = (await updateKanbanSettingApi(
        requestKanbanSetting
      )) as IKanbanSettingApiRes;
      if (responseKanbanSetting.success) {
        setKanbanSetting(responseKanbanSetting?.data);
        // setKanbanSelected(responseKanbanSetting?.kanban_project_selected);
      } else {
        notification.error({
          description: responseKanbanSetting.message || "Something went wrong!",
        });
      }
    } catch (error) {
      notification.error({
        description: (error as Error)?.message || "Something went wrong!",
      });
    } finally {
      setIsDefaultViewKanbanLoading(false);
    }
  };

  const debouncedOnSearchChange = useCallback(
    debounce(({ target: { value } }) => {
      dispatch(setProSearchValue(value));
    }, 500),
    []
  );

  useEffect(() => {
    const currentValues = {
      filter: JSON.stringify(filter),
      search: proSearchValue,
    };

    if (
      kanbanView &&
      (!isEqual(previousValues.current, currentValues) ||
        !kanbanListData.length)
    ) {
      previousValues.current = { ...currentValues };
      fetchKanbanProjectList("");
      setPageBySection({});
    }
  }, [filter, proSearchValue, kanbanView]);

  const handleDefaultViewKanabanChangeSmall = async (checked: boolean) => {
    setIsDefaultViewKanbanLoading(true);
    try {
      const requestKanbanSetting = {
        default_view: checked ? 1 : 0,
        module_id: module_id,
      };
      const responseKanbanSetting = (await updateKanbanSettingApi(
        requestKanbanSetting
      )) as IKanbanSettingApiRes;
      if (responseKanbanSetting.success) {
        setKanbanSetting(responseKanbanSetting?.data);
      } else {
        notification.error({
          description: responseKanbanSetting.message || "Something went wrong!",
        });
      }
    } catch (error) {
      notification.error({
        description: (error as Error)?.message || "Something went wrong!",
      });
    } finally {
      setIsDefaultViewKanbanLoading(false);
    }
  };
  const CARD_VIEW_CHECK_BOX = [
    {
      title: "Set as Default View",
      isChecked: kanbanSetting?.default_view?.toString() == "1",
      onClick: () => {
        const currentKan = kanbanSetting?.default_view?.toString() === "1";
        handleDefaultViewKanabanChangeSmall(!currentKan);
      },
    },
  ];

  return (
    <>
      <div className={`${mapView && "hidden"}`}>
        {/* <DashboardHeader
          searchPlaceHolder={name ? _t(`Search for ${name}`) : ""}
          viewSearch={viewSearch}
          searchVal={proSearchValue}
          setViewSearch={setViewSearch}
          searchProps={{
            placeholder: name ? _t(`Search for ${name}`) : "",
          }}
          onSearchChange={debouncedOnSearchChange}
          filterComponent={
            <ProjectFilter
              kanbanView={kanbanView}
              beforeFilterUpdateCallback={beforeFilterUpdateCallback}
            />
          }
          leftComponent={
            <>
              {!kanbanView ? (
                <>
                  <li>
                    <ButtonWithTooltip
                      tooltipTitle={_t("Card View")}
                      tooltipPlacement="top"
                      icon="fa-brands fa-trello"
                      iconClassName="h-5 w-5"
                      className="!w-7 !h-7"
                      onClick={() => setIsKanbanEnabled(true)}
                    />
                  </li>
                  <li>
                    <ButtonWithTooltip
                      tooltipTitle={_t("Map View")}
                      tooltipPlacement="top"
                      icon="fa-regular fa-location-dot"
                      iconClassName="h-5 w-5"
                      className="!w-7 !h-7"
                      onClick={() => {
                        setMapView(true);
                      }}
                    />
                  </li>
                </>
              ) : (
                <>
                  <li>
                    <ButtonWithTooltip
                      tooltipTitle={_t("Return to Dashboard")}
                      tooltipPlacement="top"
                      icon="fa-regular fa-square-list"
                      iconClassName="h-5 w-5"
                      className="!w-7 !h-7"
                      onClick={() => setIsKanbanEnabled(false)}
                    />
                  </li>
                </>
              )}
            </>
          }
          rightComponent={
            <div className="flex flex-row sm:items-center items-start sm:gap-5 gap-2">
              {PARALLEL_SITE === "1" ? (
                <li>
                  <TypographyLink
                    href={PANEL_URL + "manage_projects.php?debug=5"}
                    target="_blank"
                    className="text-13 !text-white !bg-deep-orange-500 font-semibold sm:!px-2 !px-1 !py-1 rounded !inline-block whitespace-nowrap align-middle"
                  >
                    <Typography className="!text-white sm:block hidden">
                      {_t("Back to Classic")}
                    </Typography>
                    <FontAwesomeIcon
                      className="text-base w-5 h-5 !text-white sm:hidden block"
                      icon="fa-regular fa-computer-classic"
                    />
                  </TypographyLink>
                </li>
              ) : null}
              {kanbanView && (
                <div className="md:flex hidden items-center">
                  <li>
                    <CustomCheckBox
                      className="gap-1.5 !font-medium text-primary-900 dark:text-white/90 relative sm:before:absolute sm:before:w-0.5 sm:before:h-11 sm:before:-right-1.5 sm:before:top-1/2 sm:before:-translate-y-1/2 sm:before:bg-[radial-gradient(50%_50%_at_50%_50%,#D9D9D9_0%,#d9d9d900_100%)]"
                      checked={kanbanSetting?.default_view?.toString() == "1"}
                      onChange={handleDefaultViewKanabanChange}
                      disabled={isDefaultViewKanbanLoading}
                      loadingProps={{
                        isLoading: isDefaultViewKanbanLoading,
                        className: "bg-[#ffffff]",
                      }}
                    >
                      {_t("Set as Default View")}
                    </CustomCheckBox>
                  </li>
                </div>
              )}
              {kanbanView && (
                <Popover
                  placement="bottomRight"
                  content={
                    <div className="dark:bg-dark-900 min-w-[155px]">
                      <ul className="py-2 px-1 grid gap-0.5">
                        {CARD_VIEW_CHECK_BOX.map((item: any, i) => {
                          return (
                            <li
                              className={`rounded bg-blue-50 dark:bg-dark-800`}
                              key={i}
                            >
                              <div
                                className="flex items-center justify-between cursor-pointer px-2 py-0.5 gap-1"
                                onClick={item.onClick}
                              >
                                <Typography className="text-primary-900 dark:text-white/90">
                                  {item.title}
                                </Typography>
                                {item?.isChecked && (
                                  <FontAwesomeIcon
                                    className={`text-base w-4 h-4 text-primary-900 opacity-100 `}
                                    icon="fa-regular fa-check"
                                  />
                                )}
                              </div>
                            </li>
                          );
                        })}
                      </ul>
                    </div>
                  }
                  trigger="click"
                  open={open}
                  className="flex md:hidden"
                  onOpenChange={(newOpen: boolean) => setOpen(newOpen)}
                >
                  <div className="flex relative items-center justify-center">
                    <ButtonWithTooltip
                      icon="fa-regular fa-gear"
                      tooltipTitle=""
                      tooltipPlacement="top"
                      iconClassName="h-5 w-5"
                      className="!w-7 !h-7"
                      onClick={() => {}}
                    />
                  </div>
                </Popover>
              )}
              {!isReadOnly && (
                <li>
                  <AddButton
                    onClick={() =>
                      startTransition(() => {
                        setAddProjectOpen(true);
                      })
                    }
                  >
                    {_t(singular_name ?? "Project")}
                  </AddButton>
                </li>
              )}
            </div>
          }
        /> */}
        {!kanbanView && !isKanbanLoading ? (
          <div className="pt-[41px] md:h-[calc(100vh-143px)] h-[calc(100vh-112px)] overflow-y-auto overflow-hidden">
            {/* <ReadOnlyPermissionMsg view={isReadOnly} />
            <div className="p-4">
              <div
                className={`grid min-[1536px]:grid-cols-3 md:grid-cols-2 grid-cols-1 gap-2.5 transition-all ease-in-out duration-300 ${
                  fullScreenTable
                    ? "max-h-0 overflow-hidden"
                    : "xxl:max-h-[600px] max-h-[1700px]"
                }`}
              >
                <div className="common-card min-[1536px]:order-none md:order-1 min-h-[272px]">
                  <Suspense
                    fallback={
                      <BarChartSkeleton sizeClassName="h-[200px] py-3.5" />
                    }
                  >
                    <ActionItems data={dashboardData.action_items ?? {}} />
                  </Suspense>
                </div>
                {!mapView && (
                  <div className="common-card min-[1536px]:order-none md:order-3 md:col-span-2 min-h-[272px] overflow-hidden">
                    <Suspense fallback={<SchedulerSkeleton />}>
                      <ThisWeekTasks />
                    </Suspense>
                  </div>
                )}
                <div className="common-card min-[1536px]:order-none md:order-2 min-h-[272px]">
                  <Suspense
                    fallback={
                      <BarChartSkeleton sizeClassName="h-[200px] py-3.5" />
                    }
                  >
                    <Labor data={dashboardData.labor ?? {}} />
                  </Suspense>
                </div>
                <div className="common-card min-[1536px]:order-none md:order-4 min-h-[272px]">
                  <Suspense
                    fallback={
                      <>
                        <LegendChart />
                        <div className="grid sm:grid-cols-2 grid-cols-1 relative gap-2 mt-4">
                          <PieChartSkeleton legendShow={false} />
                          <PieChartSkeleton legendShow={false} />
                        </div>
                      </>
                    }
                  >
                    <UnpaidItems data={dashboardData.unpaid_items ?? {}} />
                  </Suspense>
                </div>
                <div className="common-card min-[1536px]:order-none md:order-5 min-h-[272px]">
                  <Suspense
                    fallback={
                      <ul className="grid gap-6 px-[5px] pt-2.5">
                        {Array.from({ length: 6 }).map((_, key) => (
                          <li
                            className="flex sm:items-center items-start sm:gap-8 gap-2 flex-col sm:flex-row"
                            key={key}
                          >
                            <div className="flex items-center gap-2">
                              <div className="w-20 bg-black/10 rounded h-3 animate-pulse"></div>
                              <div className="w-7 bg-black/10 rounded h-3 animate-pulse"></div>
                            </div>
                            <div className="flex items-center w-full gap-3">
                              <div className="bg-black/10 rounded h-2 w-[calc(100%-56px)] animate-pulse"></div>
                              <div className="bg-black/10 rounded h-3 w-14 animate-pulse"></div>
                            </div>
                          </li>
                        ))}
                      </ul>
                    }
                  >
                    <ProjectsbyStatus
                      data={dashboardData.projects_by_status ?? []}
                    />
                  </Suspense>
                </div>
              </div>
              <div
                className={`w-full ${
                  fullScreenTable
                    ? "md:mt-2.5 mt-[37px]"
                    : "md:mt-[25px] mt-[55px]"
                }`}
              >
                <div className="relative h-7 z-[999] flex items-center justify-end">
                  <AccordionButton
                    onClick={() => setFullScreenTable((prev: boolean) => !prev)}
                    fullScreenTable={fullScreenTable}
                  />
                  <div className="flex justify-end md:mb-[25px] mb-20">
                    <ListTabButton
                      value={filter.tab}
                      options={GRID_BUTTON_TAB}
                      className="sm:min-w-[100px] min-w-[60px]"
                      onChange={(e: RadioChangeEvent) => {
                        let project_status =
                          "bidding,project_submittal,completed,started,unscheduled,pending";
                        let project_status_names =
                          "Bidding, Submittal, Completed, Started, Unscheduled, Pending";
                        if (e.target.value === "open") {
                          project_status =
                            "bidding,project_submittal,started,unscheduled,pending";
                          project_status_names =
                            "Bidding, Submittal, Started, Unscheduled, Pending";
                        } else if (e.target.value === "completed") {
                          project_status = "completed";
                          project_status_names = "Completed";
                        }
                        updateFilter({
                          tab: e.target.value,
                          project_status: project_status,
                          project_status_names: project_status_names,
                        });
                      }}
                      activeclassName="!bg-[#F1F4F9]"
                    />
                  </div>
                </div> */}
            <div className="p-2 bg-white dark:bg-dark-600 common-list-table rounded">
              <Suspense fallback={null}>
                <ProjectList
                // setAddProjectOpen={setAddProjectOpen}
                // search={proSearchValue}
                />
              </Suspense>
            </div>
            {/* </div>
            </div> */}
          </div>
        ) : (
          <div className="pt-[41px] md:h-[calc(100dvh-116px)] h-[calc(100dvh-84px)] overflow-y-auto overflow-hidden">
            {/* <ReadOnlyPermissionMsg
              view={isReadOnly}
              className="px-4 sm:pt-4 pt-1"
              textClassName="sm:text-13 text-xs flex justify-center items-end sm:h-auto h-7 sm:leading-auto leading-[15px]"
            />
            {!isKanbanLoading ? (
              isLoading || kanbanListData.length ? (
                <div className="flex pt-4 pb-2.5 px-1.5 overflow-x-auto">
                  <KanbanList
                    list={kanbanListData} //  kanban data
                    setList={setKanbanListData}
                    loading={!kanbanListData.length || isLoading}
                    kanbanSelected={kanbanSelected}
                    collapseClick={(projectColumn) => {
                      handleCollapse(
                        projectColumn?.key?.toString(),
                        projectColumn?.is_collapse_card?.toString()
                      );
                    }}
                    cardDetailsClick={(project) => {
                      setProjectToBeUpdate(project);
                      navigate(
                        `${routes.MANAGE_PROJECT.url}/${project?.id}${
                          !hasEnoughAccessofProjectFinanceTabModule
                            ? "/details"
                            : ""
                        }`
                      );
                    }}
                    loadMore={(val) => {
                      handleLoadMore(val);
                    }}
                    childLoader={loadingChild}
                    colum={{
                      headerName: "name",
                      parentId: "item_id",
                      count: "total_count",
                      collapse: "is_collapse_card",
                      color: "status_color",
                      child: "projects",
                      childCard: {
                        cardId: "id", // child card id pass
                        cardFirstFirst: "project_name",
                        cardMiddleFirst: "customer_name",
                        // cardLastFirst: "customer_name",
                        // cardMiddleSecond: "customer_name",
                        // cardImg: "user_profile_image",
                        // imgName: "assignee",
                      },
                    }}
                    handleColumnDragDropEnd={handleDragDropEnd}
                    handleCardDragDropEnd={handleCardDragDropEnd}
                    isReadOnly={isReadOnly}
                    handleMouseMove={(project) => {
                      setProjectToBeUpdate(project);
                    }}
                  >
                    <div onClick={(e) => e.stopPropagation()}>
                      {(module_access === "full_access" ||
                        module_access === "own_data_access") && (
                        <ProjectListTableDropdownItems
                          data={projectToBeUpdate}
                          icon="fa-solid fa-ellipsis-h"
                          refreshTable={() => fetchKanbanProjectList("")}
                          className="m-0 hover:!bg-[#0000000f] invisible group-hover/kanbanitem:visible"
                          iconClassName="text-primary-900/80 group-hover/buttonHover:text-primary-900"
                          contentClassName="menu-h-scroll"
                        />
                      )}
                    </div>
                  </KanbanList>
                </div>
              ) : (
                <></>
              )
            ) : (
              <></>
            )} */}
          </div>
        )}

        {/* <Suspense
          fallback={
            <Spin className="w-full h-[calc(100vh-194px)] flex items-center justify-center" />
          }
        >
          <AddProjectWrapper
            setAddProjectOpen={setAddProjectOpen}
            addProjectOpen={addProjectOpen}
          />
        </Suspense> */}
      </div>
      {/* 
      {mapView && (
        <div
          className={`p-4 h-[calc(100vh-143px)] overflow-y-auto overflow-hidden relative ${
            !mapView && "hidden"
          }`}
        >
          <div className="absolute z-10 top-[30px] left-[30px]">
            <Button
              icon={
                <FontAwesomeIcon
                  className="text-base w-3.5 h-3.5 text-primary-900 dark:text-white/80"
                  icon="fa-regular fa-arrow-left"
                />
              }
              className="px-2.5 hover:!text-primary-900 hover:!border-primary-900 transition-all"
              onClick={() => {
                setMapView(false);
                setSchedulerReloadKey(Date.now());
              }}
            >
              {_t("Back")}
            </Button>
          </div>
          <div className="w-full scheduler_ui project_map_view relative google-map">
            <ProjectEventsMapView
              type="map"
              schedulerReloadKey={schedulerReloadKey}
              filter={filter}
            />
          </div>
        </div>
      )} */}
    </>
  );
};

const ManageProjects = () => {
  return (
    <ProjectStoreProvider>
      <ManageProjectsCom />
    </ProjectStoreProvider>
  );
};

export default React.memo(ManageProjects);

export { ErrorBoundary };
