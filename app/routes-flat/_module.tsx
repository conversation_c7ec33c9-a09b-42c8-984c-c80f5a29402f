// css
import ApexCharts from "~/assets/minify/apexcharts.style.css";
import lightGalleryCss from "~/assets/minify/light-gallery.style.css";
import lightGallery from "lightgallery/css/lightgallery.css";
import lgZoom from "lightgallery/css/lg-zoom.css";
import lgAutoplay from "lightgallery/css/lg-autoplay.css";
import lgFullscreen from "lightgallery/css/lg-fullscreen.css";
import lgThumbnail from "lightgallery/css/lg-thumbnail.css";
import reactQuillStyle from "react-quill/dist/quill.snow.css";
import QuillCss from "~/assets/minify/quill-editor.style.css";
import cropper from "cropperjs/dist/cropper.css";
import AgGridStylesClass from "~/assets/minify/ag-grid.style.css";
import AgGridStyles from "ag-grid-community/styles/ag-grid.css";
import AgThemeAlpineStyles from "ag-grid-community/styles/ag-theme-alpine.css";
import GridStackCss from "node_modules/gridstack/dist/gridstack.min.css";
import DhtmlxSchedulerCss from "dhtmlx-scheduler/codebase/dhtmlxscheduler.css";
import ReactGanttCss from "@dhx/react-gantt/dist/react-gantt.css";
import CalendarCss from "~/assets/minify/calendar.style.css";
import PdfViewer from "@react-pdf-viewer/core/lib/styles/index.css";
import PdfViewerDef from "@react-pdf-viewer/default-layout/lib/styles/index.css";
import rdtStylesheet from "remix-development-tools/index.css";
import FroalaEditor from "froala-editor/css/froala_style.min.css";
import FroalaEditorPKG from "froala-editor/css/froala_editor.pkgd.min.css";
import style from "~/assets/minify/style.style.css";
import antDesign from "~/assets/minify/antd.style.css";
import tailwind from "~/assets/minify/tailwind.style.css";
// import Dhtmlx from "~/assets/minify/dhtmlx.style.css";
import DhtmlxTerrace from "~/assets/minify/dhtmlxmenu_dhx_terrace.style.css";
import { LicenseManager } from "ag-grid-enterprise";
LicenseManager.setLicenseKey(
  "CompanyName=Contractor Foreman,LicensedGroup=Contractor Foreman - Project Reports,LicenseType=MultipleApplications,LicensedConcurrentDeveloperCount=1,LicensedProductionInstancesCount=1,AssetReference=AG-035166,SupportServicesEnd=22_November_2023_[v2]_MTcwMDYxMTIwMDAwMA==8fea7f8210059ec205e9fe8f273a3636"
);
import { redirect } from "@remix-run/node";
import {
  Links,
  LiveReload,
  Meta,
  Outlet,
  Scripts,
  ScrollRestoration,
  useLoaderData,
} from "@remix-run/react";
import { useEffect, useMemo, useState } from "react";
import { UNDER_MAINTENANCE } from "~/shared/constants";
import { ErrorBoundary } from "~/shared/components/molecules/errorBoundary/ErrorBoundary";
import { ConfigProvider } from "~/components/third-party/ant-design/antd";
import { getWindow } from "~/utils/windowProcess";
import { mainLoader } from "../services/root.server";
import { setGConfigAuth, setPageIsIframe } from "~/zustand";
import { setAuthorizationExpired } from "~/zustand/global/config/action";
import { useTranslation } from "react-i18next";
import isEmpty from "lodash/isEmpty";
import UnknownError from "~/components/common/unknown-error";
import { Provider } from "react-redux";
import { store } from "~/redux/store";
import Layout from "~/shared/components/organisms/layoutSection";
import { PaddleScript } from "~/shared/components/molecules/paddleScript";
import { fortAwesomeRegularIconAdd } from "~/shared/utils/helper/fontAwesome/libraryAdd/common/regular";
import { fortAwesomeSolidIconAdd } from "~/shared/utils/helper/fontAwesome/libraryAdd/common/solid";
import { fortAwesomeDuotoneIconAdd } from "~/shared/utils/helper/fontAwesome/libraryAdd/common/duotone";
import { fortAwesomeLightIconAdd } from "~/shared/utils/helper/fontAwesome/libraryAdd/common/light";
import { fortAwesomeBrandsIconAdd } from "~/shared/utils/helper/fontAwesome/libraryAdd/common/brands";
import { CurrencyFormatterProvider } from "~/shared/components/organisms/currencyFormatterContext/CurrencyFormatterContext";
import { getCurrentMenuModule } from "~/zustand/global/config/slice";
import { sanitizeString } from "~/helpers/helper";
import { isNotAuthURL } from "~/shared/utils/helper/authRoute";
import { routes } from "~/route-services/routes";
import UpdateCompanyTypeModal from "~/components/modals/update-company-type";
import EventLoggerLoader from "~/shared/components/molecules/eventLoggerLoader";
import WindowNotification from "~/shared/components/atoms/windowNotification/WindowNotification";
import { MainLoader } from "~/shared/components/atoms/mainLoader";
import { setGlobalUser } from "~/zustand/global/user/action";
import { getGlobalUser } from "~/zustand/global/user/slice";
import { AntDesignApp } from "~/shared/components/atoms/antDesignApp";
import { USER_PLANES } from "~/data/pages";

export const links: TLinksFunction = () => [
  { rel: "stylesheet", href: tailwind },
  { rel: "stylesheet", href: ApexCharts },
  { rel: "stylesheet", href: AgGridStyles },
  { rel: "stylesheet", href: AgThemeAlpineStyles },
  { rel: "stylesheet", href: AgGridStylesClass },
  { rel: "stylesheet", href: lightGallery },
  { rel: "stylesheet", href: lgZoom },
  { rel: "stylesheet", href: lgThumbnail },
  { rel: "stylesheet", href: lgAutoplay },
  { rel: "stylesheet", href: lgFullscreen },
  { rel: "stylesheet", href: lightGalleryCss },
  { rel: "stylesheet", href: cropper },
  { rel: "stylesheet", href: reactQuillStyle },
  { rel: "stylesheet", href: QuillCss },
  { rel: "stylesheet", href: GridStackCss },
  { rel: "stylesheet", href: DhtmlxSchedulerCss },
  { rel: "stylesheet", href: ReactGanttCss },
  { rel: "stylesheet", href: CalendarCss },
  { rel: "stylesheet", href: PdfViewer },
  { rel: "stylesheet", href: PdfViewerDef },
  { rel: "stylesheet", href: FroalaEditor },
  { rel: "stylesheet", href: FroalaEditorPKG },
  { rel: "stylesheet", href: style },
  { rel: "stylesheet", href: antDesign },
  // { rel: "stylesheet", href: Dhtmlx },
  { rel: "stylesheet", href: DhtmlxTerrace },
  // froala_style add
  {
    rel: "stylesheet",
    href: "https://editor-latest.s3.amazonaws.com/v3/css/froala_style.min.csss",
  },
  {
    rel: "stylesheet",
    href: "https://open-sesame-cell.vercel.app/opensesame-cell.css",
  },
];

export const handle = {
  // In the handle export, we can add a i18n key with namespaces our route
  // will need to load. This key can be a single string or an array of strings.
  // TIP: In most cases, you should set this to your defaultNS from your i18n config
  // or if you did not set one, set it to the i18next default namespace "translation"
  i18n: "common",
};

export const loader: TLoaderFunction = async (loaderParams) => {
  if (UNDER_MAINTENANCE) {
    return redirect("/maintenance");
  }
  return await mainLoader(loaderParams);
};

// Fort Awesome Library Add icons
fortAwesomeRegularIconAdd();
fortAwesomeSolidIconAdd();
fortAwesomeDuotoneIconAdd();
fortAwesomeLightIconAdd();
fortAwesomeBrandsIconAdd();

const Module = () => {
  const {
    authorization_expired,
    authorization,
    user,
    PAGE_IS_IFRAME,
    ...loader
  } = useLoaderData<AppLoader>();

  const [userLoading, setUserLoading] = useState<boolean>(true);

  const globalUser: IInitialGlobalData["user"] = getGlobalUser();
  const { full_name, email } = globalUser || {};

  useEffect(() => {
    if (user) {
      if (
        user.success &&
        user.data &&
        (!globalUser || (globalUser && !Object.values(globalUser).length))
      ) {
        const { group_id } = user.data || {};

        const [key] = Object.entries(USER_PLANES).find(([key, plan_id]) => {
          if (plan_id?.toString() === group_id?.toString()) {
            return true;
          }
          return false;
        }) || ["", ""];
        DEFAULT_CLIENT_CONFIG.plan = key?.toString();

        window.TIMEZONE = {
          timezone_full_text: user.data.timezone_full_text,
          timezone_id: user.data.timezone_id,
          timezone_user_offset: user.data.timezone_user_offset,
          timezone_utc_tz_id: user.data.timezone_utc_tz_id,
        };

        setGlobalUser(user.data);
      }
      setUserLoading(false);
    }
  }, [user]);

  const currentModule: IInitialGlobalData["config"]["current_module"] =
    getCurrentMenuModule();
  const { name: module_name } = currentModule || {};

  // Translation functions
  const { i18n } = useTranslation();

  const [updateCompanyTypeOpen, setUpdateCompanyTypeOpen] =
    useState<boolean>(false);

  useEffect(() => {
    window.ENV = {
      ...window.ENV,
      ...loader,
    };
  }, [
    loader.NODE_API3_URL,
    loader.PHP_API_URL,
    loader.PANEL_URL,
    loader.PANEL_TYPE,
    loader.FEATURE_REQUEST_URL,
    loader.GOOGLE_DRIVE_CLIENT_SECRET,
    loader.GOOGLE_DRIVE_OAUTH_SCOPE,
    loader.GOOGLE_DRIVE_API_KEY,
    loader.GOOGLE_DRIVE_APP_ID,
    loader.GOOGLE_DRIVE_CLIENT_ID,
    loader.DROPBOX_APP_KEY,
    loader.PDFJS_EXPRESS_KEY,
    loader.PADDLE_CHURN_KEY,
    loader.CHARGBEE_CHURN_KEY,
    loader.PADDLE_CHECKOUT_URL,
    loader.CDN_URL,
    loader.AWS_URL_PREFIX_REGION,
    loader.QUICKBOOK_URL,
    loader.FROALA_EDITOR_KEY,
    loader.STRIPE_PUBLISHABLE_KEY,
    loader.ALLOW_EXTERNAL_SCRIPT,
    loader.ALLOW_MIXPANEL_SCRIPT,
    loader.ALLOW_CHURNZERO_SCRIPT,
    loader.MAIN_PAGE_URL,
    loader.ENABLE_SKU_FIELD,
    loader.ENABLE_UNIT_DROPDOWN,
    loader.ENABLE_ALL_CLICK,
  ]);

  useEffect(() => {
    if (authorization && !isEmpty(authorization)) {
      setGConfigAuth(authorization);
    }
  }, [authorization]);

  useEffect(() => {
    if (authorization_expired) {
      setAuthorizationExpired(authorization_expired);
    }
  }, [authorization_expired]);

  useEffect(() => {
    window.ENV = {
      ...(window.ENV || {}),
      PAGE_IS_IFRAME: Boolean(PAGE_IS_IFRAME),
    };
    if (PAGE_IS_IFRAME) {
      setPageIsIframe(Boolean(PAGE_IS_IFRAME));
    }
  }, [PAGE_IS_IFRAME]);

  const subTitle: string = useMemo(() => {
    let title = "";
    if (full_name?.trim()) {
      title += HTMLEntities.decode(sanitizeString(full_name.trim()));
    }
    if (email?.trim()) {
      title += ` [${email.trim() ?? ""}]`;
    }
    return title;
  }, [full_name, email]);

  useEffect(() => {
    let tempTitle = "CF";

    const pathname =
      typeof window !== "undefined" ? window.location.pathname : "";

    if (!module_name) {
      tempTitle =
        pathname === routes.DASHBOARD.url
          ? `CF: ${HTMLEntities.decode(
              sanitizeString(module_name)
            )}: ${subTitle}`
          : !isNotAuthURL(pathname) ||
            pathname === routes.TRAININGS.url ||
            pathname === routes.UPDATE.url ||
            !subTitle
          ? "Contractor Foreman"
          : `Contractor Foreman: ${subTitle}`;
    } else {
      tempTitle = `CF: ${HTMLEntities.decode(
        sanitizeString(module_name)
      )}: ${subTitle}`;
      const panelType = window.ENV?.PANEL_TYPE;
      if (panelType === "dev" || panelType === "localhost") {
        tempTitle = `|_${panelType.toUpperCase()}_| ${tempTitle}`;
      }
    }
    document.title = tempTitle; // Directly update the document's title
  }, [subTitle, module_name]);

  return (
    <html lang={i18n.language} {...(!isEmpty(i18n) ? { dir: i18n.dir() } : {})}>
      <head>
        <title>CF</title>
        <meta charSet="utf-8" />
        <meta name="viewport" content="width=device-width,initial-scale=1" />
        <meta name="keywords" content="Contractor Foreman" />
        <meta name="description" content="Contractor Foreman User Panel" />
        <meta name="robots" content="noindex" />
        <meta name="googlebot" content="noindex, nofollow" />
        <meta name="google" content="notranslate" />
        <meta name="theme-color" content="#223558" />
        <meta name="msapplication-TileColor" content="#223558" />
        <Meta />
        {loader.PANEL_TYPE === "dev" && (
          <link rel="stylesheet" href={rdtStylesheet} />
        )}
        <Links />
        <script src="https://contractor-foreman.hellonext.co/embed.js"></script>
      </head>
      <body className="bg-[#F8F8F9] max-h-[calc(100dvh-31px)] overflow-y-auto overflow-hidden">
        <MainLoader loading={userLoading}>
          {user?.success ? (
            <CurrencyFormatterProvider>
              <Provider store={store}>
                <ConfigProvider
                  theme={{
                    token: {
                      fontFamily: "Open Sans",
                    },
                  }}
                >
                  <AntDesignApp>
                    <Layout>
                      <Outlet />
                      <UpdateCompanyTypeModal
                        open={updateCompanyTypeOpen}
                        closeModalHandler={() =>
                          setUpdateCompanyTypeOpen(false)
                        }
                      />
                    </Layout>
                  </AntDesignApp>
                </ConfigProvider>
              </Provider>
            </CurrencyFormatterProvider>
          ) : (
            <UnknownError message={user?.message || "User data not found"} />
          )}
        </MainLoader>
        {user?.success && (
          <>
            <script // this script important for get translation data
              dangerouslySetInnerHTML={{
                __html: `
                window.ENV = ${JSON.stringify(loader)};
                var $zoho = $zoho || {};
                $zoho.salesiq = $zoho.salesiq || {
                  widgetcode: "a60815904d859e16d13e50f38adbd4240af0f407e28f6d2fdc6cb3577649ad8e3c114aff4948b17814d4f7ca25cf90ee",
                  values: {},
                  ready: function() {}
                };
                window.zoho = $zoho
              `,
              }}
            />
            {/* <script // this script important for Open Sesame Cell
              dangerouslySetInnerHTML={{
                __html: `
             window.OpenSesameCellConfig = {
                    config: {
                      logo: "https://cdn.contractorforeman.net/assets/images/favicon/favicon-32x32.png",
                      primaryColor: "#ffffff",
                      secondaryColor: "#223558",
                      placeholderText:
                        "Ask me anything about your account, billing, or support...",
                      suggestedQuestions: [
                        "How do I update my billing information?",
                        "Show me my recent transactions.",
                        "What are the support hours?",
                        "Can I get a summary of my account activity?",
                      ],
                      borderRadius: 32,
                      glowColor: "#223558",
                      accentColor: "#223558",
                      textColor: "#223558",
                      authToken: {
                        "authorization": "Bearer ${authorization}",
                      },
                      userId: "${user?.data?.user_id?.toString()}",
                      cellId: "5c72d9ec-021f-41e2-839a-e9ccf1cfaa4f",
                      readOnly: true,
                    },
              };
             (function () {
                setTimeout(() => {
                  if (!window.ENV || window.ENV.PAGE_IS_IFRAME !== true) {
                    const s = document.createElement("script");
                    s.src = "https://open-sesame-cell.vercel.app/widget.js";
                    s.async = true;
                    document.head.appendChild(s);
                  }
                }, 1000);
              })();
            `,
              }}
            /> */}
            <PaddleScript />
            <WindowNotification />
            <EventLoggerLoader />
          </>
        )}
        <ScrollRestoration />
        <Scripts />
        <LiveReload />
      </body>
    </html>
  );
};

const windowObj = getWindow();

let ModuleExport = Module;
if (windowObj && windowObj?.ENV?.PANEL_TYPE === "dev") {
  const { withDevTools } = require("remix-development-tools");
  ModuleExport = withDevTools(ModuleExport);
}

export default ModuleExport; // Exporting ModuleExport

export { ErrorBoundary };
